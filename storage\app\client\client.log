[2025-10-03 05:53:11] local.ERROR: {"error":"Symfony\\Component\\<PERSON>rrorHandler\\Error\\FatalError","message":"Maximum execution time of 120 seconds exceeded","url":"http:\/\/www.mydomain.strangedomains.local\/broadcasting\/auth","code":0,"ip":"127.0.0.1","method":"POST","user_id":"guest","trace":"#0 {main}"}  
[2025-10-03 05:58:28] local.INFO: Domain History: Domain cancellation request initiated by mjb<PERSON><PERSON><PERSON>@gmail.com.  
[2025-10-03 05:59:58] local.INFO: Domain History: Domain cancellation request initiated by mjb<PERSON>lo<PERSON>@gmail.com.  
[2025-10-03 06:00:09] local.INFO: Domain History: Domain cancellation request initiated by mjb<PERSON><EMAIL>.  
[2025-10-03 06:00:30] local.INFO: Domain History: Domain cancellation request initiated by mjb<PERSON><PERSON><PERSON>@gmail.com.  
[2025-10-03 06:11:44] local.INFO: ProcessDomainDeletionRefunds: Refund limit reached. Marking 1 registration domains as refunded without processing refund  
[2025-10-03 06:11:44] local.INFO: Marked domain as refunded without processing refund for domain name: twelvetoone.net  
