<?php

namespace App\Modules\Domain\Services\ViewServices;

use App\Exceptions\UserDomainException;
use App\Modules\Cart\Services\UserCart;
use App\Modules\Category\Services\CategoryService;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainContact;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Requests\ShowListRequest;
use App\Modules\DomainClassificationApi\Services\DomainClassificationApiService;
use App\Traits\CursorPaginate;
use App\Traits\UserContact;
use App\Modules\MarketPlace\Services\MarketDomainService;
use App\Modules\MarketPlace\Constants\MarketConstants;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ViewDomainService
{
    use CursorPaginate, UserContact, UserLoggerTrait;

    public static function instance(): self
    {
        $viewDomainService = new self;

        return $viewDomainService;
    }

    public function getDomainView(int $id): array
    {
        return [
            'data' => $this->getDomainDetails($id),
            'domainClassificationData' => (new DomainClassificationApiService)->fetchDomain($id),
        ];
    }

    public function getAllProducts($request)
    {
        $builder = MarketDomainService::instance()->getProductIndex($request);

        $data = CursorPaginate::cursor($builder, $this->paramToURI($request));

        return $this->assignProductDetails($data);
    }

    public function getAll(ShowListRequest $request): array
    {
        $data = $this->getDomainIndex($request);

        return $this->assignIndexDetails($data);
    }

    public function getDomainEdit(int $id): array
    {
        $data['data'] = $this->getDomainDetails($id);

        return $this->assignEditDetails($data);
    }

    public function getDomainDetails(int $id): object
    {
        $data = DomainBuilder::instance()->get($id);
        $this->checkViewEmptyData($data);

        return $this->assignDomainDetails($data);
    }

    // PRIVATE FUNCTIONS

    private function assignProductDetails($data)
    {
        $data['status_type'] = MarketConstants::getProductsTab();

        return $data;
    }

    private function assignIndexDetails($data)
    {
        $data['categories'] = CategoryService::instance()->getCategoryNames();
        $data['status_type'] = DomainStatus::getDomainsTab();
        $cart = new UserCart;
        $data['cartCount'] = $cart->getTotalDomain();
        $data['searchedDomains'] = session('searched_domains', []);
        $data['domainRefundData'] = $this->getDomainRefundData();

        return $data;
    }

    private function assignEditDetails($data)
    {
        $registryId = $data['data']->registry_id;
        $data['contacts'] = json_decode(json_encode(UserContact::getContactList($registryId, Auth::id())), true);
        $data['categories'] = CategoryService::instance()->getCategoryList();

        return $data;
    }

    private function assignDomainDetails($data)
    {
        $contacts_id = json_decode($data->contacts_id, true);
        $contacts_id[DomainContact::REGISTRANT] = $data->user_contact_registrar_id;
        $contactsArray = array_values($contacts_id);
        $contactDetails = UserContact::getDomainContacts($contactsArray);
        $registrant = $contactDetails[$contacts_id[DomainContact::REGISTRANT]];
        $admin_contact = $contactDetails[$contacts_id[DomainContact::ADMIN]];
        $tech_contact = $contactDetails[$contacts_id[DomainContact::TECH]];
        $billing_contact = $contactDetails[$contacts_id[DomainContact::BILLING]];

        $data->registrant = $registrant;
        $data->admin_contact = $admin_contact;
        $data->tech_contact = $tech_contact;
        $data->billing_contact = $billing_contact;
        $data->client_status = json_decode($data->client_status, true);

        $category = CategoryService::instance()->getById($data->user_category_id);
        $data->category = $category;
        $data->nameservers = json_decode($data->nameservers, true);

        return $data;
    }

    private function checkViewEmptyData($data): void
    {
        if (!$data) {
            throw new UserDomainException(404, 'Page not found.', '');
        }

        $contacts_id = json_decode($data->contacts_id, true);
        if (empty($contacts_id)) {
            throw new UserDomainException(404, 'Page not found.', '');
        }
    }

    private function getDomainIndex(ShowListRequest $request): array
    {
        $builder = DomainBuilder::instance()->getDomainIndex($request);

        return CursorPaginate::cursor($builder, $this->paramToURI($request));
    }

    private function paramToURI($request): array
    {
        $param = [];

        if ($request->has('status')) {
            $param[] = 'status=' . $request->status;
        }

        if ($request->has('orderby')) {
            $param[] = 'orderby=' . $request->orderby;
        }

        if ($request->has('tld')) {
            $param[] = 'tld=' . $request->tld;
        }

        if ($request->has('category')) {
            $param[] = 'category=' . $request->category;
        }

        if ($request->has('nameserver')) {
            $param[] = 'nameserver=' . $request->nameserver;
        }

        return $param;
    }

    private function getDomainRefundData(): array
    {
        $refundData = DB::table('domain_registration_refund_limit')
            ->where('action', true)
            ->select('limit', 'times_triggered')
            ->first();

        if (!$refundData) {
            return [
                'limit' => 50,
                'times_triggered' => 0,
                'shouldShowWarning' => false
            ];
        }

        $limit = $refundData->limit;
        $timesTriggered = $refundData->times_triggered;
        $warningThreshold = floor($limit * 0.9);
        $shouldShowWarning = $timesTriggered >= $warningThreshold;

        return [
            'limit' => $limit,
            'times_triggered' => $timesTriggered,
            'shouldShowWarning' => $shouldShowWarning
        ];
    }
}
