<?php

namespace App\Console\Commands\Domain;

use Illuminate\Console\Command;
use App\Modules\Domain\Services\DomainDeletionService;
use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Support\Facades\DB;

class ProcessDomainDeletionRefunds extends Command
{
    protected $signature = 'domain:deletion-refund';

    protected $description = 'Process refunds for all approved domain deletions';

    public function handle()
    {
        $refundLimit = DB::table('domain_registration_refund_limit')->first();

        if (!$refundLimit) {
            app(AuthLogger::class)->error('ProcessDomainDeletionRefunds: Domain registration refund limit not found');
            return 1;
        }

        $registrationRefunds = DomainDeletionService::instance()->getRefundableRegistrationDomains();
        $renewalRefunds = DomainDeletionService::instance()->getRefundableRenewalDomains();

        if (empty($registrationRefunds) && empty($renewalRefunds)) {
            app(AuthLogger::class)->info('ProcessDomainDeletionRefunds: No eligible refunds to process.');
            return 0;
        }

        if ($refundLimit->times_triggered >= $refundLimit->limit) {
            if (!empty($registrationRefunds)) {
                app(AuthLogger::class)->info('ProcessDomainDeletionRefunds: Refund limit reached. Marking ' . count($registrationRefunds) . ' registration domains as refunded without processing refund');
                $this->markDomainsAsRefundedWithoutRefund($registrationRefunds, 'is_refunded');
            }

            if (!empty($renewalRefunds)) {
                app(AuthLogger::class)->info('ProcessDomainDeletionRefunds: Refund limit reached. Marking ' . count($renewalRefunds) . ' renewal domains as refunded without processing refund');
                $this->markDomainsAsRefundedWithoutRefund($renewalRefunds, 'is_renewal');
            }
        } else {
            if (!empty($registrationRefunds)) {
                app(AuthLogger::class)->info('ProcessDomainDeletionRefunds: Processing ' . count($registrationRefunds) . ' registration refunds...');
                DomainDeletionService::instance()->refundRegistration($registrationRefunds);
            }

            if (!empty($renewalRefunds)) {
                app(AuthLogger::class)->info('ProcessDomainDeletionRefunds: Processing ' . count($renewalRefunds) . ' renewal refunds...');
                DomainDeletionService::instance()->refundRenewal($renewalRefunds);
            }
        }

        $this->info('Domain deletion refunds processed successfully.');
        return 0;
    }
}
