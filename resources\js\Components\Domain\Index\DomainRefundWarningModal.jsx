import React from "react";
import Modal from "@/Components/Modal";
import DangerButton from "@/Components/DangerButton";
import PrimaryButton from "@/Components/PrimaryButton";

export default function DomainRefundWarningModal({
    user,
    isModalOpen,
    closeModal,
    domainRefundData = { limit: 50, times_triggered: 0, shouldShowWarning: false },
}) {
    const RefundWarningMessage = () => {
        const { limit, times_triggered } = domainRefundData;
        const percentage = Math.round((times_triggered / limit) * 100);

        return (
            <div className="space-y-4">
                <p>
                    <strong>Important Notice:</strong> This domain was registered within the last 5 days and may not be eligible for a refund according to our refund policy.
                </p>
                <p>
                    You have newly registered domains. Please be aware that continued deletion of newly registered domains may affect your refund eligibility.
                </p>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <p className="text-yellow-800">
                        <strong>Refund Limit Warning:</strong> You have used {times_triggered} out of {limit} domain deletions ({percentage}% of your limit).
                        You are approaching the maximum number of refundable domain deletions.
                    </p>
                </div>
                <p>
                    Please note that refunds for newly registered domains are subject to our terms and conditions. Domains registered within five (5) days of creation may have limited refund eligibility.
                </p>
                <p className="text-sm text-gray-600">
                    For more information about our refund policy, please contact our support team.
                </p>
            </div>
        );
    };

    const ConfirmationAction = () => {
        return (
            <div className="flex justify-end space-x-2">
                <PrimaryButton
                    onClick={() => closeModal(true)}
                    className="bg-primary hover:bg-gray-700 px-4 py-2 rounded-lg text-white"
                >
                    Continue with Deletion
                </PrimaryButton>
                <DangerButton onClick={() => closeModal(false)}>
                    Cancel
                </DangerButton>
            </div>
        );
    };

    return (
        <Modal show={isModalOpen} onClose={() => closeModal(false)}>
            <div className="px-8 py-6 space-y-6">
                <div>
                    <h1 className="font-bold text-xl text-orange-600">Domain Refund Warning</h1>
                    <div className="mx-6 px-4 border-l-[6px] border-orange-500 mt-4">
                        <RefundWarningMessage />
                    </div>
                </div>
                <ConfirmationAction />
            </div>
        </Modal>
    );
}
